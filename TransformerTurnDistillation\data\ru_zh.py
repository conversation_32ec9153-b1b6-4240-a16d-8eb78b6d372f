import os
import torch
from torch.utils.data import Dataset
from tqdm import tqdm
from torchtext.data import get_tokenizer
import jieba
from torchtext.vocab import build_vocab_from_iterator
import zhconv
import re


class RuZhTranslationDataset(Dataset):
    def __init__(self, filepath, use_cache=True):
        self.row_count = self.get_row_count(filepath)
        self.use_cache = use_cache

        # 加载词典和token
        self.ru_vocab = self.get_ru_vocab(filepath)
        self.zh_vocab = self.get_zh_vocab(filepath)
        self.ru_tokens = self.load_tokens(filepath, self.ru_tokenizer, self.ru_vocab, "构建俄文tokens", 'ru')
        self.zh_tokens = self.load_tokens(filepath, self.zh_tokenizer, self.zh_vocab, "构建中文tokens", 'zh')

    def __getitem__(self, index):
        return self.ru_tokens[index], self.zh_tokens[index]

    def __len__(self):
        return self.row_count

    def load_tokens(self, filepath, tokenizer, vocab, desc, lang):
        dir_path = os.path.dirname(filepath)
        cache_file = os.path.join(dir_path, f"tokens_list_{lang}.pt")
        if self.use_cache and os.path.exists(cache_file):
            print(f"正在加载缓存文件[{cache_file}]，请稍候...")
            return torch.load(cache_file, map_location="cpu")

        tokens_list = []
        with open(filepath, encoding='utf-8') as f:
            for line in tqdm(f, desc=desc, total=self.row_count):
                parts = line.strip().split('\t')
                if len(parts) >= 4:  # ru-zh.txt format has at least 4 columns
                    if lang == 'ru':
                        text = parts[2].strip()  # Russian text is in column 3 (0-indexed: 2)
                    else:  # zh
                        text = zhconv.convert(parts[3].strip(), 'zh-cn')  # Chinese text is in column 4 (0-indexed: 3)
                    
                    if text:  # Only process non-empty text
                        tokens = tokenizer(text)
                        token_indices = [vocab[token] for token in tokens]
                        token_tensor = torch.tensor([vocab["<s>"]] + token_indices + [vocab["</s>"]])
                        tokens_list.append(token_tensor)

        if self.use_cache:
            torch.save(tokens_list, cache_file)
        return tokens_list

    def get_row_count(self, filepath):
        count = 0
        with open(filepath, encoding='utf-8') as f:
            for line in f:
                parts = line.strip().split('\t')
                if len(parts) >= 4 and parts[2].strip() and parts[3].strip():
                    count += 1
        return count

    def ru_tokenizer(self, line):
        # Enhanced Russian tokenizer - handles Cyrillic characters properly
        # Convert to lowercase and find all word tokens (including Cyrillic)
        tokens = re.findall(r'[а-яё\w]+', line.lower(), re.UNICODE)
        return tokens

    def zh_tokenizer(self, line):
        return list(jieba.cut(line))

    def get_ru_vocab(self, filepath):
        def yield_ru_tokens():
            with open(filepath, encoding='utf-8') as f:
                print("---开始构建俄文词典---")
                for line in tqdm(f, desc="构建俄文词典", total=self.row_count):
                    parts = line.strip().split('\t')
                    if len(parts) >= 4 and parts[2].strip():
                        russian = parts[2].strip()
                        yield self.ru_tokenizer(russian)

        dir_path = os.path.dirname(filepath)
        ru_vocab_file = os.path.join(dir_path, "vocab_ru.pt")
        if self.use_cache and os.path.exists(ru_vocab_file):
            ru_vocab = torch.load(ru_vocab_file, map_location="cpu")
        else:
            ru_vocab = build_vocab_from_iterator(
                yield_ru_tokens(),
                min_freq=1,
                specials=["<s>", "</s>", "<pad>", "<unk>"]
            )
            ru_vocab.set_default_index(ru_vocab["<unk>"])
            if self.use_cache:
                torch.save(ru_vocab, ru_vocab_file)
        return ru_vocab

    def get_zh_vocab(self, filepath):
        def yield_zh_tokens():
            with open(filepath, encoding='utf-8') as f:
                print("---开始构建中文词典---")
                for line in tqdm(f, desc="构建中文词典", total=self.row_count):
                    parts = line.strip().split('\t')
                    if len(parts) >= 4 and parts[3].strip():
                        chinese = zhconv.convert(parts[3].strip(), 'zh-cn')
                        yield self.zh_tokenizer(chinese)

        dir_path = os.path.dirname(filepath)
        zh_vocab_file = os.path.join(dir_path, "vocab_zh.pt")
        if self.use_cache and os.path.exists(zh_vocab_file):
            zh_vocab = torch.load(zh_vocab_file, map_location="cpu")
        else:
            zh_vocab = build_vocab_from_iterator(
                yield_zh_tokens(),
                min_freq=1,
                specials=["<s>", "</s>", "<pad>", "<unk>"]
            )
            zh_vocab.set_default_index(zh_vocab["<unk>"])
            if self.use_cache:
                torch.save(zh_vocab, zh_vocab_file)
        return zh_vocab


if __name__ == '__main__':
    dataset = RuZhTranslationDataset(r"D:\2025-up\MT\TransformerTurnDistillation\TransformerTurnDistillation\data\ru-zh.txt")
    print("句子数量为:", dataset.row_count)
    print(dataset.ru_tokenizer("Я русский токенизатор."))  # ['я', 'русский', 'токенизатор']
    print("俄文词典大小:", len(dataset.ru_vocab))
    # 输出俄文词典前10个索引
    print(dict((i, dataset.ru_vocab.lookup_token(i)) for i in range(10)))
    print("中文词典大小:", len(dataset.zh_vocab))
    # 输出中文词典前10个索引
    print(dict((i, dataset.zh_vocab.lookup_token(i)) for i in range(10)))
    # 输出俄文前10个句子对应的字典索引编号
    print(dict((i, dataset.ru_tokens[i]) for i in range(min(10, len(dataset.ru_tokens)))))
    # 输出中文前10个句子对应的字典索引编号
    print(dict((i, dataset.zh_tokens[i]) for i in range(min(10, len(dataset.zh_tokens)))))
