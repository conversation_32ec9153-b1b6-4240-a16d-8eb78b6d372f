#!/usr/bin/env python3
"""
Test script to verify Russian-Chinese translation setup
"""

import torch
from data.ru_zh import RuZhTranslationDataset
from torch.utils.data import DataLoader
from model.transformer import TranslationModel

def test_data_loading():
    """Test that the Russian-Chinese dataset loads correctly"""
    print("Testing Russian-Chinese data loading...")
    
    # Load dataset
    data_dir = "TransformerTurnDistillation/data/ru-zh.txt"
    dataset = RuZhTranslationDataset(data_dir)
    
    print(f"Dataset size: {len(dataset)} sentence pairs")
    print(f"Russian vocabulary size: {len(dataset.ru_vocab)}")
    print(f"Chinese vocabulary size: {len(dataset.zh_vocab)}")
    
    # Test a few samples
    print("\nFirst 3 samples:")
    for i in range(min(3, len(dataset))):
        ru_tokens, zh_tokens = dataset[i]
        ru_text = " ".join(dataset.ru_vocab.lookup_tokens(ru_tokens.tolist()))
        zh_text = " ".join(dataset.zh_vocab.lookup_tokens(zh_tokens.tolist()))
        print(f"Sample {i+1}:")
        print(f"  Russian: {ru_text}")
        print(f"  Chinese: {zh_text}")
        print()
    
    return dataset

def test_model_creation(dataset):
    """Test that the model can be created with Russian-Chinese vocabularies"""
    print("Testing model creation...")
    
    device = torch.device('cpu')  # Use CPU for testing
    max_seq_length = 32
    d_model = 256
    
    # Create model
    model = TranslationModel(d_model, dataset.ru_vocab, dataset.zh_vocab, max_seq_length, device)
    model = model.to(device)
    
    print(f"Model created successfully!")
    print(f"Source vocab size: {len(dataset.ru_vocab)}")
    print(f"Target vocab size: {len(dataset.zh_vocab)}")
    print(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")
    
    return model

def test_tokenization():
    """Test Russian and Chinese tokenization"""
    print("Testing tokenization...")
    
    dataset = RuZhTranslationDataset("TransformerTurnDistillation/data/ru-zh.txt")
    
    # Test Russian tokenization
    ru_text = "Привет мир"
    ru_tokens = dataset.ru_tokenizer(ru_text)
    print(f"Russian text: '{ru_text}'")
    print(f"Russian tokens: {ru_tokens}")
    
    # Test Chinese tokenization  
    zh_text = "你好世界"
    zh_tokens = dataset.zh_tokenizer(zh_text)
    print(f"Chinese text: '{zh_text}'")
    print(f"Chinese tokens: {zh_tokens}")

def test_batch_processing():
    """Test batch processing with DataLoader"""
    print("Testing batch processing...")
    
    dataset = RuZhTranslationDataset("TransformerTurnDistillation/data/ru-zh.txt")
    
    # Simple collate function for testing
    def simple_collate_fn(batch):
        ru_batch, zh_batch = zip(*batch)
        return ru_batch, zh_batch
    
    # Create DataLoader
    dataloader = DataLoader(dataset, batch_size=4, shuffle=False, collate_fn=simple_collate_fn)
    
    # Test first batch
    batch = next(iter(dataloader))
    ru_batch, zh_batch = batch
    
    print(f"Batch size: {len(ru_batch)}")
    print(f"First Russian sequence length: {len(ru_batch[0])}")
    print(f"First Chinese sequence length: {len(zh_batch[0])}")

if __name__ == "__main__":
    print("=== Russian-Chinese Translation Setup Test ===\n")
    
    try:
        # Test data loading
        dataset = test_data_loading()
        print("✓ Data loading test passed\n")
        
        # Test tokenization
        test_tokenization()
        print("✓ Tokenization test passed\n")
        
        # Test model creation
        model = test_model_creation(dataset)
        print("✓ Model creation test passed\n")
        
        # Test batch processing
        test_batch_processing()
        print("✓ Batch processing test passed\n")
        
        print("🎉 All tests passed! Russian-Chinese translation setup is working correctly.")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
