import torch
import torch.nn as nn
import math


class PositionalEncoding(nn.Module):
    "位置编码"
    def __init__(self, d_model, dropout, device, max_len=5000):
        super(PositionalEncoding, self).__init__()
        self.dropout = nn.Dropout(p=dropout)

        # 初始化 Shape 为 (max_len, d_model) 的 PE (positional encoding)
        pe = torch.zeros(max_len, d_model).to(device)
        position = torch.arange(0, max_len).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2) * -(math.log(10000.0) / d_model))

        # 计算 PE[:, 0::2]
        pe[:, 0::2] = torch.sin(position * div_term)
        # 计算 PE[:, 1::2]
        pe[:, 1::2] = torch.cos(position * div_term)

        # 为了方便计算，在最外面再利用 unsqueeze 函数添加 batch=1 的维度
        pe = pe.unsqueeze(0)

        # 如果一个参数不参与梯度下降，但又希望保存 model 的时候将其保存下来
        # 这个时候就可以用 register_buffer 函数
        self.register_buffer("pe", pe)

    def forward(self, x):
        """
        x 为 embedding 后的 inputs，如 (1, 7, 128)，batch size 为 1, 7 个单词，单词维度为 128
        """
        # 将 x 和 positional encoding 相加。
        x = x + self.pe[:, :x.size(1)].requires_grad_(False)
        return self.dropout(x)
class TranslationModel(nn.Module):

    def __init__(self, d_model, src_vocab, tgt_vocab, max_seq_length, device, dropout=0.1):
        super(TranslationModel, self).__init__()
        self.device = device

        # 定义原句子的 embedding
        self.src_embedding = nn.Embedding(len(src_vocab), d_model, padding_idx=2)
        # 定义目标句子的 embedding
        self.tgt_embedding = nn.Embedding(len(tgt_vocab), d_model, padding_idx=2)
        # 定义 positional encoding
        self.positional_encoding = PositionalEncoding(d_model, dropout, device, max_len=max_seq_length)
        # 定义 Transformer
        self.transformer = nn.Transformer(d_model, dropout=dropout, batch_first=True)

        # 定义最后的预测层，这里并没有定义 Softmax，而是把他放在了模型外。
        self.predictor = nn.Linear(d_model, len(tgt_vocab))

    def forward(self, src, tgt):
        """
        进行前向传递，输出为 Decoder 的输出。注意，这里并没有使用 self.predictor 进行猜测，因为训练和推理行为不太一样，所以放在了模型外面。
        :param src: 原 batch 后的句子，如[[0, 12, 34, ..., 1, 2, 2, ...], ...]
        :param tgt: 目标 batch 后的句子，如[[0, 74, 56, ..., 1, 2, 2, ...], ...]
        :return: Transformer 的输出，或者说是 TransformerDecoder 的输出。
        """

        """ 生成 tgt_mask，即阶梯形的 mask，例如：
        # [[0., -inf, -inf, -inf, -inf],
        [
            [0., 0., -inf, -inf, -inf],
            [0., 0., 0., -inf, -inf],
            [0., 0., 0., 0., -inf],
            [0., 0., 0., 0., 0.]
        ]

        tgt.size()[-1]
        为目标句子的长度。"""

        tgt_mask = nn.Transformer.generate_square_subsequent_mask(tgt.size()[-1]).to(self.device)
        # 掩盖住原句子中<cpad>的部分，如[[False, False, False,..., True, True,...],...]
        src_key_padding_mask = TranslationModel.get_key_padding_mask(src)
        # 掩盖住目标句子中<cpad>的部分
        tgt_key_padding_mask = TranslationModel.get_key_padding_mask(tgt)

        # 对 src 和 tgt 进行编码
        src = self.src_embedding(src)
        tgt = self.tgt_embedding(tgt)
        # 给 src 和 tgt 的 token 增加位置信息
        src = self.positional_encoding(src)
        tgt = self.positional_encoding(tgt)

        # 将准备好的数据送给 transformer
        out = self.transformer(src, tgt,
                               tgt_mask=tgt_mask,
                               src_key_padding_mask=src_key_padding_mask,
                               tgt_key_padding_mask=tgt_key_padding_mask)

        """
        这里直接返回 transformer 的结果。因为训练和推理时的行为不一样，所以在该模型外再进行线性层的预测。
        """
        return out

    @staticmethod
    def get_key_padding_mask(tokens, pad_idx=2):
        return tokens == pad_idx

if __name__ == '__main__':
    device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
    from data.cmn_eng import TranslationDataset

    dataset = TranslationDataset(r"D:\2025-up\MT\TransformerTurnDistillation\data\cmn-eng\cmn.txt")
    model = TranslationModel(512, dataset.en_vocab, dataset.zh_vocab, 50, device)
    model = model.to(device)

    en = "hello world"
    input = torch.tensor([0] + dataset.en_vocab(dataset.en_tokenizer(en)) + [1]).unsqueeze(0)
    input = input.to(device)

    zh = "你"
    output = torch.tensor([0] + dataset.zh_vocab(dataset.zh_tokenizer(zh))).unsqueeze(0)
    output = output.to(device)

    result = model(input, output)
    print(result)

    predict = model.predictor(result[:, -1])
    print(predict)

    # 找出最大值的 indexa
    y = torch.argmax(predict, dim=1).cpu().item()
    print(dataset.zh_vocab.lookup_tokens([y]))